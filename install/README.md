# install cmdb-agent

Install cmdb-agent using a script...

## running on it kvm-master
```bash
wget -O -  https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/ops/cmdb-agent_kvm-master_install-linux-amd64.sh  | bash
```

## running on it kvm-node
```bash
wget -O -  https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/ops/cmdb-agent_kvm-node_install-linux-amd64.sh  | bash
```

## running on ops nodes

```bash
wget -O -  https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/ops/cmdb-agent_ops-node_install-linux-amd64.sh  | sudo bash
```

## After Install

```bash
# 根据主机的环境执行
## 开发环境执行
echo "CMDB_MACHINE_ENV=dev" > /root/cmdb-agent-dir/envfile
## 测试环境执行
echo "CMDB_MACHINE_ENV=test" > /root/cmdb-agent-dir/envfile
## 预发环境执行
echo "CMDB_MACHINE_ENV=staging" > /root/cmdb-agent-dir/envfile
## 生产环境执行
echo "CMDB_MACHINE_ENV=prod" > /root/cmdb-agent-dir/envfile

# Restart cmdb-agent
systemctl restart cmdb-agent
```
