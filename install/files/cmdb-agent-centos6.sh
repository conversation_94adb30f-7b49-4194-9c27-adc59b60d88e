#!/bin/bash
#
# cmdb-agent          Start up the cmdb-agent server daemon
#
# chkconfig: - 55 25
# description: cmdb-agent
#调用系统脚本方法
source /etc/profile
RETVAL=0
prog="cmdb-agent"
CMDB=/sbin/cmdb-agent
cmdbPath="/sbin/cmdb-agent"
cmdbLog=/root/cmdb-agent-dir/logs/cmdb-agent.log
#CMDB_MACHINE_ENV=test
cmdb(){
  if [ -x "$cmdbPath" ]; then
    /sbin/cmdb-agent run -r http://cmdb-agent-proxy.test.gifshow.com/cmdb-web/api/v1/os/report --it --kvm-master >> /root/cmdb-agent-dir/logs/cmdb-agent.log 2>&1 &
  fi
}
. /etc/rc.d/init.d/functions
lock_file=/var/lock/subsys/cmdb-agent
pid_file=/var/run/cmdb-agent.pid

#服务管理:start,stop,status
start() {
    echo -n $"Starting $prog: "
    cmdb &
    retval=$?
    echo
    [ $retval -eq 0 ] && touch $lock_file
 #   [ $retval -eq 0 ] && add_dhclient_servers
    return $retval
}

stop() {
    echo -n $"Stopping $prog: "
    killproc $prog
    retval=$?
    echo
    [ $retval -eq 0 ] && rm -f $lock_file
    return $retval
}

restart() {
    stop
    start
}

reload() {
    restart
}

force_reload() {
    restart
}

rh_status() {
    status $prog
}

rh_status_q() {
    rh_status >/dev/null 2>&1
}


case "$1" in
    start)
        rh_status_q && exit 0
        $1
        ;;
    stop)
        rh_status_q || exit 0
        $1
        ;;
    restart)
        $1
        ;;
    reload)
        rh_status_q || exit 7
        $1
        ;;
    force-reload)
        force_reload
        ;;
    status)
        rh_status
        ;;
    condrestart|try-restart)
        rh_status_q || exit 0
        restart
        ;;
    online|offline|cyclelogs)
        rh_status_q || exit 7
        chrony_command $1
        ;;
    command)
        rh_status_q || exit 7
        chrony_command "$2"
        ;;
    *)
        echo $"Usage: $0 {start|stop|status|restart|condrestart|try-restart|reload|force-reload|cyclelogs|online|offline|command}"
        exit 2
esac
exit $?