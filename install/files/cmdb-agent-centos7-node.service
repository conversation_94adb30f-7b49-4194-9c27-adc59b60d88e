[Unit]
Description=The cmdb agent

[Service]
#Type=forking
#PIDFile=/run/cmdb.pid
# Nginx will fail to start if /run/nginx.pid already exists but has the wrong
# SELinux context. This might happen when running `nginx -t` from the cmdline.
# https://bugzilla.redhat.com/show_bug.cgi?id=1268621
ExecStartPre=/usr/bin/rm -f /run/cmdb.pid
ExecStart=/sbin/cmdb-agent run -r http://cmdb-agent-proxy.test.gifshow.com/cmdb-web/api/v1/os/report --it
#ExecStartPos=/usr/bin/pgrep cmdb-agent > /run/cmdb.pid
#ExecReload=/bin/kill -s HUP $MAINPID
ExecStop=/usr/bin/pkill -9 cmdb-agent
WorkingDirectory=/root/cmdb-agent-dir
EnvironmentFile=-/root/cmdb-agent-dir/envfile
#ExecStopPost=/usr/bin/rm -f /run/cmdb.pid
KillSignal=SIGQUIT
TimeoutStopSec=5
KillMode=process
PrivateTmp=true
Restart=always
[Install]
WantedBy=multi-user.target