#!/bin/sh

echo "stopping cmdb-agent..."
systemctl stop cmdb-agent
echo "cmdb-agent stopped..."

echo "downloading cmdb-agent binary file..."
wget -q https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/ops/cmdb-agent-linux-amd64 -O /sbin/cmdb-agent
if [ $? -ne 0 ];then
  echo "Download cmdb-agent binary file failed..."
fi

chmod +x /sbin/cmdb-agent

mkdir -p /root/cmdb-agent-dir/logs

touch /root/cmdb-agent-dir/envfile

echo "starting cmdb-agent..."
if [ -f /etc/redhat-release ];then
  centos_version=$(awk '{print $4}' /etc/redhat-release|awk -F. '{print $1}')
  if [ $centos_version -eq 7 ]; then
    wget -q https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/ops/cmdb-agent-centos7-node.service -O /usr/lib/systemd/system/cmdb-agent.service
    systemctl daemon-reload
    systemctl enable cmdb-agent
    systemctl restart cmdb-agent
    systemctl status cmdb-agent
  else
    wget -q https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/ops/cmdb-agent-centos6-node.sh -O /etc/init.d/cmdb-agent
    chmod +x /etc/init.d/cmdb-agent
    chkconfig cmdb-agent on
    service cmdb-agent restart
    service cmdb-agent status
  fi
else
  wget -q https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/ops/cmdb-agent-ubuntu-node.service -O /lib/systemd/system/cmdb-agent.service
  systemctl daemon-reload
  systemctl enable cmdb-agent
  systemctl restart cmdb-agent
  systemctl status cmdb-agent
fi


