package cache

import (
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/api"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/internal/config"
	"time"
)

var (
	LocalCache *Cache
)

type Cache struct {
	HostReportRequestCache *HostReportRequestCache
}

func NewCache() *Cache {
	return &Cache{HostReportRequestCache: NewHostReportRequestCache()}
}

type HostReportRequestCache struct {
	HostReportRequest *api.HostReportRequest
	Timer *time.Timer
	IsValid bool
}

func NewHostReportRequestCache() *HostReportRequestCache {
	return &HostReportRequestCache{
		HostReportRequest: nil,
		Timer:           time.NewTimer(config.COLLECTOR_CACHE_TIMEOUT * time.Second),
	}
}

func UseCache() {
	LocalCache = NewCache()
	LocalCache.HostReportRequestCache = NewHostReportRequestCache()

	go func() {
		for {
			select {
			case <- LocalCache.HostReportRequestCache.Timer.C:
				LocalCache.HostReportRequestCache.IsValid = false
				LocalCache.HostReportRequestCache.Timer.Reset(config.COLLECTOR_CACHE_TIMEOUT * time.Second)
			}
		}
	}()
}