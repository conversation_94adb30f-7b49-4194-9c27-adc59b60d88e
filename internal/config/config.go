package config

const (
	REPORT_HOST = "127.0.0.1"
	REPORT_PORT = "20085"
	REPORT_URI = "/cmdb-web/api/v1/os/report"
	REPORT_METHOD = "POST"
	REPORT_PROTOCOL = "http"
	REPORT_URL = REPORT_PROTOCOL + "://" + REPORT_HOST + ":" + REPORT_PORT + REPORT_URI
	CONTENT_TYPE = "application/json"
	REPORT_DURATION = 120
	TICKET = "VKImaQj2FB1SDc3l"

	//LOG_FILE_DIR = "./logs"
	LOG_FILE_NAME = "cmdb-agent.log"
	//LOG_FILE_PATH = LOG_FILE_DIR + "/" + LOG_FILE_NAME
	LOG_FILE_ABS_PATH = "/root/cmdb-agent-dir/logs/" + LOG_FILE_NAME
	LOG_LIMIT_IN_MB = 100
	LOG_CLEAN_PERIOD = 300

	COLLECTOR_CACHE_TIMEOUT = 1800

	AGENT_RESTART_KEY = "/agent/action/restart"
	AGENT_START_KEY = "/agent/action/start"
	AGENT_STOP_KEY = "/agent/action/stop"
	AGENT_UPDATE_KEY = "/agent/action/update"

	KIM_WEBHOOK = "https://kim-robot.kwaitalk.com/api/robot/send?key=b32249fb-68df-4813-8ff2-ebf1cdd11b7d"
	KIM_MSG_TYPE = "markdown"
)

