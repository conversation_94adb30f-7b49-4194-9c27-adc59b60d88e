package collector

import (
	"fmt"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/internal/common"
	"reflect"
	"strings"
)

type Collector interface {
	Collect() error
}

type CollectorList struct {
	Collectors []Collector
	ReportURL string
}

func NewCollectorList(targets []string, hb *HostBase) *CollectorList {
	cls := &CollectorList{}
	for _, t := range targets {
		if t == "host" {
			cls.Collectors = append(cls.Collectors, NewHostCollector(hb))
		}
	}
	return cls
}

type collectTask struct {
	StartTS int64
	collect reflect.Value
	EndTS   int64
}

func registerTasks(c Collector) {
	v := reflect.ValueOf(c)
	t := reflect.TypeOf(c)

	switch ty := c.(type) {
	case *HostCollector:
		for i := 0; i < v.NumMethod(); i++ {
			if strings.HasPrefix(t.Method(i).Name, common.COLLECT_TASK_PREFIX) && strings.HasSuffix(t.Method(i).Name, common.COLLECT_TASK_SUFFIX){
				ty.tasks = append(ty.tasks, &collectTask{collect: v.Method(i)})
			}
		}
	default:
		fmt.Printf("Unknown collector type %v \n", ty)
	}
}

