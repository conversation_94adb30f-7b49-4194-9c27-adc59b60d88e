package collector

import (
	"errors"
	"fmt"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/api"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/internal/common"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/internal/utils"
	"github.com/mitchellh/go-ps"
	"github.com/shirou/gopsutil/host"
	"github.com/shirou/gopsutil/mem"
	gopsutil_net "github.com/shirou/gopsutil/net"
	"github.com/sirupsen/logrus"
	"github.com/yumaojun03/dmidecode"
	"io/ioutil"
	"net"
	"os"
	"os/exec"
	"reflect"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"
)

var (
	once sync.Once
)

type HostCollector struct {
	tasks []*collectTask
	Host *api.Host
	HostBase *HostBase
}

type HostBase struct {
	IsItOs bool
	IsKvmMaster bool
}

func NewHostCollector(hb *HostBase) Collector {
	return &HostCollector{
		HostBase: hb,
	}
}

func (hc *HostCollector) beforeTaskHook() {
	logrus.Info("HostCollector beforeTaskHook...")
	hc.Host = &api.Host{}
	hc.Host.IsItOs = hc.HostBase.IsItOs
	hc.Host.IsKvmMaster = hc.HostBase.IsKvmMaster
}

func (hc *HostCollector) Collect() error {
	hc.beforeTaskHook()
	once.Do(func() {
		registerTasks(hc)
	})
	if err := hc.executeTasks(); err != nil {
		return err
	}
	hc.afterTaskHook()
	return nil
}

func (hc *HostCollector) afterTaskHook() {
	logrus.Info("HostCollector afterTaskHook...")

	ok := hc.getSnAfterHook()
	if !ok {
		logrus.Info("Get SN Failed...")
	}

	hc.Host.IsK8s = hc.getIsK8sAfterHook()

	hc.Host.MacAddr = hc.getMacAfterHook()
}

func (hc *HostCollector) getSnAfterHook() bool {
	if dmi, err := dmidecode.New(); err == nil {
		infos, _ := dmi.System()
		if len(infos) > 0 {
			hc.Host.Sn = infos[0].SerialNumber
		}
	}
	if hc.Host.Sn == "" {
		return false
	}
	return true
}

func (hc *HostCollector) getIsK8sAfterHook() bool {
	// check kubelet process
	processes, _ := ps.Processes()
	var hasKubeletProcess bool
	for _, process := range processes {
		if strings.ToLower(process.Executable()) == common.KUBELET_PROCESS_NAME {
			hasKubeletProcess = true
		}
	}

	if !hasKubeletProcess {
		return false
	}

	// check kubelet port
	conn, err := net.DialTimeout("tcp", net.JoinHostPort(hc.Host.IP, common.KUBELET_PORT), time.Second)
	if err != nil {
		return false
	}
	if conn != nil {
		defer conn.Close()
		return true
	}
	return false
}

func (hc *HostCollector) getMacAfterHook() string {
	ips :=  make(map[string]string)

	interfaces, err := net.Interfaces()
	if err != nil {
		return ""
	}

	for _, i := range interfaces {
		byName, err := net.InterfaceByName(i.Name)
		if err != nil {
			return ""
		}
		addrs, err := byName.Addrs()
		for _, v := range addrs {
			ips[strings.Split(v.String(), "/")[0]] = byName.Name
		}
	}
	byName, _ := net.InterfaceByName(ips[hc.Host.IP])
	return byName.HardwareAddr.String()
}

func (hc *HostCollector) executeTasks() error {
	var err error
	wg := &sync.WaitGroup{}
	wg.Add(len(hc.tasks))

	for _, t := range hc.tasks {
		go func(t *collectTask) {
			defer wg.Done()
			t.StartTS = time.Now().Unix()
			errValue := t.collect.Call([]reflect.Value{})[0]
			if errValue.Interface() == nil {
				t.EndTS = time.Now().Unix()
			} else {
				err = errors.New(fmt.Sprintf("%v", errValue.Interface()))
			}

		}(t)
	}
	wg.Wait()
	return err
}


func (hc *HostCollector) CollectCpuInfo() error {
	hc.Host.CpuCores = runtime.NumCPU()
	return nil
}

func (hc *HostCollector) CollectMemInfo() error {
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		return err
	}
	hc.Host.TotalMemory = memInfo.Total
	return nil
}

func (hc *HostCollector) CollectHostnameInfo() error {
	var err error
	hc.Host.Hostname, err = os.Hostname()
	return err
}

func (hc *HostCollector) CollectKvmMacListInfo() error {
	var err error
	kvmUtil := utils.NewKvmUtil()
	hc.Host.MacAddrList = kvmUtil.GetKvmMacList()
	return err
}

func (hc *HostCollector) CollectIsVirtualInfo() error {
	osInfo, err := host.Info()
	if err != nil {
		return err
	}
	if osInfo.OS == "linux" {
		if hasSystemd() {
			c := exec.Command("bash", "-c", "systemd-detect-virt")
			output, _ := c.CombinedOutput()
			res := strings.Trim(string(output), "\n")
			logrus.Info(res)
			if res != "none" {
				hc.Host.IsVirtual = false
			} else {
				hc.Host.IsVirtual = true
			}
			return nil
		}
		if !hasLspci() {
			c := exec.Command("bash", "-c", "yum install pciutils -y")
			if _, err := c.CombinedOutput(); err != nil {
				_ = utils.KimEventUtils.SendMsg(fmt.Sprintf("%s Install pciutils failed, reason: %s\n",osInfo.Hostname, err))
			}
			return err
		}
		if hasLspci() {
			c := exec.Command("bash", "-c", "lspci | grep -i Virt | wc -l")
			output, err := c.CombinedOutput()
			logrus.Info(string(output))
			if err != nil {
				logrus.Errorf("execute lspci failed, reason: %s\n", string(output))
				return nil
			}
			if virtualLine, err := strconv.Atoi(strings.TrimSuffix(strings.TrimSpace(string(output)), "\n")); err != nil {
				logrus.Errorf("get virtual flag failed, reason: %s\n", err)
				return nil
			} else {
				if virtualLine > 0 {
					hc.Host.IsVirtual = true
					return nil
				}
				hc.Host.IsVirtual = false
			}
		}
	} else {
		if osInfo.VirtualizationSystem == "" && osInfo.VirtualizationRole != "guest" {
			hc.Host.IsVirtual = false
		} else {
			hc.Host.IsVirtual = true
		}
		return nil
	}

	return nil
}

// Get preferred outbound ip of this machine
func GetOutboundIP() (net.IP, error){
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)

	return localAddr.IP, nil
}


func (hc *HostCollector) CollectIpInfo() error {
	ip, err := GetOutboundIP()
	if err != nil {
		return err
	}
	hc.Host.IP = ip.String()
	//addrs, err := net.InterfaceAddrs()
	//if err != nil {
	//	return err
	//}
	//for _, addr := range addrs {
	//	ipAddr, ok := addr.(*net.IPNet)
	//	if !ok {
	//		continue
	//	}
	//	if ipAddr.IP.IsLoopback() {
	//		continue
	//	}
	//	if !ipAddr.IP.IsGlobalUnicast() {
	//		continue
	//	}
	//	if ipAddr.IP.To4() == nil {
	//		continue
	//	}
	//	hc.Host.IP = ipAddr.IP.String()
	//	break
	//}
	return nil
}

func (hc *HostCollector) CollectOsInfo() error {
	if osInfo, err := host.Info(); err != nil {
		fmt.Println("Collect OsInfo failed...")
		return err
	} else {
		hc.Host.OsType = osInfo.OS
		hc.Host.OsPlatform = osInfo.Platform
		hc.Host.OsPlatformVersion = osInfo.PlatformVersion
		hc.Host.OsKernelVersion = osInfo.KernelVersion
		return nil
	}
}

func (hc *HostCollector) CollectLabelInfo() error {
	hc.Host.Label = "cmdb-agent"
	return nil
}

func (hc *HostCollector) CollectEnvInfo() error {
	hc.Host.Env = os.Getenv("CMDB_MACHINE_ENV")
	switch strings.ToLower(hc.Host.Env) {
	case strings.ToLower(common.ENV_DEV):
		hc.Host.Env = common.ENV_DEV
	case strings.ToLower(common.ENV_TEST):
		hc.Host.Env = common.ENV_TEST
	case strings.ToLower(common.ENV_STAGING):
		hc.Host.Env = common.ENV_STAGING
	case strings.ToLower(common.ENV_PROD):
		hc.Host.Env = common.ENV_PROD
	default:
		hc.Host.Env = common.ENV_UNKNOWN
	}
	return nil
}

func (hc *HostCollector) CollectNodeExporterPortInfo() error {
	processes, _ := ps.Processes()
	var found bool
	for _, process := range processes {
		if strings.ToLower(process.Executable()) == common.NODE_EXPORTER_PROCESS_NAME {
			hc.Host.NodeExporterPort, found = getPortForProcess(process)
			break
		}
	}
	if !found {
		logrus.Warn("NodeExporter port not found.")
	}
	return nil
}

func (hc *HostCollector) CollectPublicKeysInfo() error {
	osInfo, err := host.Info()
	if err != nil {
		return err
	}
	if osInfo.OS == "linux" {
		homeDirs := getHomeDirs()
		for _, homeDir := range homeDirs {
			username := getUsernameByPath(homeDir)

			// Get public key info
			publicKeyFiles := getPublicKeyFiles(homeDir)
			for _, publicKeyFile := range publicKeyFiles {

				if fileExisted(fmt.Sprintf(publicKeyFile), "file") {
					fileContentBytes, _ := ioutil.ReadFile(publicKeyFile)
					fileContent := string(fileContentBytes)
					publicKeyInfo := strings.Split(fileContent, " ")
					if len(publicKeyInfo) >= 3 && (publicKeyInfo[0] == "ssh-rsa" || publicKeyInfo[0] == "ssh-dsa") {
						hc.Host.PublicKeysInfoList = append(hc.Host.PublicKeysInfoList, api.OsPublicKeysInfo{
							PublicKeyPath:  publicKeyFile,
							PublicKeyType:  publicKeyInfo[0],
							PublicKeyValue: publicKeyInfo[1],
							LoginInfo:      strings.ReplaceAll(publicKeyInfo[2], "\n", ""),
							Username:       username,
						})

					}
				}
			}

			// Get auth keys info
			authorizedKeysFile := fmt.Sprintf("%s/.ssh/authorized_keys", homeDir)
			if fileExisted(fmt.Sprintf(authorizedKeysFile), "file") {
				fileContentBytes, _ := ioutil.ReadFile(authorizedKeysFile)
				fileContent := string(fileContentBytes)
				authorizedKeysLineList := strings.Split(fileContent, "\n")

				for _, authorizedKeysLine := range authorizedKeysLineList {
					authorizedKeysInfo := strings.Split(authorizedKeysLine, " ")
					if len(authorizedKeysInfo) >= 3 && (authorizedKeysInfo[0] == "ssh-rsa" || authorizedKeysInfo[0] == "ssh-dsa") {
						hc.Host.AuthorizedKeysInfoList = append(hc.Host.AuthorizedKeysInfoList, api.OsAuthorizedKeysInfo{
							AuthorizedKeysPath:  authorizedKeysFile,
							AuthorizedKeysType:  authorizedKeysInfo[0],
							AuthorizedKeysValue: authorizedKeysInfo[1],
							LoginInfo:           strings.ReplaceAll(authorizedKeysInfo[2], "\n", ""),
							Username: 			 username,
						})
					}

				}
			}
		}
	}

	return nil
}

func getPortForProcess(process ps.Process) (port int, found bool) {
	connectionStatList, err := gopsutil_net.ConnectionsPid("tcp", int32(process.Pid()))
	if err != nil {
		return 0, false
	}
	for _, connectionStat := range connectionStatList {
		if strings.ToUpper(connectionStat.Status) == "LISTEN" {
			return int(connectionStat.Laddr.Port), true
		}
	}
	return 0, false
}

func getHomeDirs() []string {
	homeDirs := make([]string, 10)
	homeDirs = append(homeDirs, "/root")
	if _, err := os.Stat("/home"); err == nil {
		files, err := ioutil.ReadDir("/home")
		if err == nil {
			for _, file := range files {
				if file.IsDir() { homeDirs = append(homeDirs, fmt.Sprintf("/home/<USER>", file.Name())) }
			}
		}
	}
	return homeDirs
}

func fileExisted(filePath string, fileType string) bool {
	if fileInfo, err := os.Stat(filePath); err != nil {
		return false
	} else {
		if fileType == "dir" && fileInfo.IsDir() {
			return true
		}
		if fileType == "file" && !fileInfo.IsDir() {
			return true
		}
	}

	return false
}

func getPublicKeyFiles(homeDir string) []string {
	publicKeyFiles := make([]string, 10)
	publicKeyFiles = append(publicKeyFiles, fmt.Sprintf("%s/.ssh/id_rsa.pub", homeDir))
	publicKeyFiles = append(publicKeyFiles, fmt.Sprintf("%s/.ssh/id_dsa.pub", homeDir))
	publicKeyFiles = append(publicKeyFiles, fmt.Sprintf("%s/.ssh/id_ecdsa.pub", homeDir))
	publicKeyFiles = append(publicKeyFiles, fmt.Sprintf("%s/.ssh/id_ed25519.pub", homeDir))
	publicKeyFiles = append(publicKeyFiles, fmt.Sprintf("%s/.ssh/id_rsa1.pub", homeDir))
	return publicKeyFiles
}

func getUsernameByPath(homeDir string) string {
	homeSubPaths := strings.Split(homeDir, "/")
	return strings.Replace(homeSubPaths[len(homeSubPaths) - 1], "\n", "", -1)
}

func hasSystemd() bool {
	c := exec.Command("bash", "-c", "which systemd-detect-virt")

	if _, err := c.CombinedOutput(); err != nil {
		return false
	}
	return true
}

func hasLspci() bool {
	c := exec.Command("bash", "-c", "which lspci")

	if _, err := c.CombinedOutput(); err != nil {
		return false
	}
	return true
}

func insPciutils() error {
	c := exec.Command("bash", "-c", "yum install pciutils -y")

	if _, err := c.CombinedOutput(); err != nil {
		return err
	}
	return nil
}