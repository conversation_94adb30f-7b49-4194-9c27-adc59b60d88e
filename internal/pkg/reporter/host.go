package reporter

import (
	"bytes"
	"encoding/json"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/api"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/cmd/cmdb-agent/version"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/internal/cache"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/internal/common"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/internal/config"
	"github.com/sirupsen/logrus"
	"io/ioutil"
	"net/http"
	"time"
)

type HostReporter struct {
	HostInfo *api.Host
}

func NewHostReporter(hostInfo *api.Host) Reporter {
	return &HostReporter{HostInfo: hostInfo}
}

func (r *HostReporter) Report(reportURL string)  {

	hostReportRequest := &api.HostReportRequest{
		Host:   r.HostInfo,
		Ticket: config.TICKET,
		AgentVersionInfo: &api.AgentVersionInfo{
			AgentVersion:   version.Version,
			AgentBuildTs:   version.BuildTS,
			AgentGitHash:   version.GitHash,
			AgentGitBranch: version.GitBranch,
		},
	}
	cache.LocalCache.HostReportRequestCache.HostReportRequest = hostReportRequest
	cache.LocalCache.HostReportRequestCache.IsValid = true
	hostInfo, _ := json.Marshal(hostReportRequest)
	payload := bytes.NewReader(hostInfo)
	client := http.Client{
		Timeout:       time.Second *  10,
	}
	if reportURL == "" {
		reportURL = config.REPORT_URL
	}
	request, _ := http.NewRequest(config.REPORT_METHOD, reportURL, payload)
	request.Header.Add("Content-Type", config.CONTENT_TYPE)
	logrus.Infof("Sending %s to cmdb server", string(hostInfo))
	response, err := client.Do(request)
	if err != nil {
		logrus.Errorln("Report to cmdb server failed...")
		return
	}
	defer response.Body.Close()
	r.handleResponse(response)
}

func (r *HostReporter) ReportCache(reportURL string, hostReportRequest interface{})  {
	logrus.Info("Report Host using cache...")
	hostInfo, _ := json.Marshal(hostReportRequest)
	payload := bytes.NewReader(hostInfo)
	client := http.Client{
		Timeout:       time.Second *  10,
	}
	if reportURL == "" {
		reportURL = config.REPORT_URL
	}
	request, _ := http.NewRequest(config.REPORT_METHOD, reportURL, payload)
	request.Header.Add("Content-Type", config.CONTENT_TYPE)
	logrus.Infof("Sending %s to cmdb server", string(hostInfo))
	response, err := client.Do(request)
	if err != nil {
		logrus.Errorln("Report to cmdb server failed...")
		print(err.Error())
		return
	}
	defer response.Body.Close()
	r.handleResponse(response)
}

func(r *HostReporter) handleResponse(response *http.Response) {
	body, _ := ioutil.ReadAll(response.Body)
	logrus.Info(string(body))
	var resp = common.HostReportResponse{
		Code: -1,
	}
	_ = json.Unmarshal(body, &resp)
	if resp.Message == "OK" {
		if resp.Result {
			logrus.Printf("Report %s Host info success", r.HostInfo.IP)
		} else {
			logrus.Printf("Report %s Host info failed, Reason: Host Exist", r.HostInfo.IP)
		}
	} else {
		logrus.Info("Report Host info failed, Reason: Internal Error")
	}
}

