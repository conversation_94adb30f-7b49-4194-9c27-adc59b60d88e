package reporter

import (
	"github.com/sirupsen/logrus"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/internal/pkg/collector"
)

type Reporter interface {
	Report(reportURL string)
	ReportCache(reportURL string, data interface{})
}


type ReporterList struct {
	Reporters []Reporter
}

func NewReporterList(cls *collector.CollectorList) *ReporterList {
	reporters := &ReporterList{};
	for _, cl := range cls.Collectors {
		switch v := cl.(type) {
		case *collector.HostCollector:
			reporters.Reporters = append(reporters.Reporters, NewHostReporter(v.Host))
		default:
			logrus.Info("Unknown collector type")
		}
	}
	return reporters
}

//func (rs *ReporterList) DoReport() {
//	for _, reporter := range rs.Reporters {
//		reporter.Report()
//	}
//}

