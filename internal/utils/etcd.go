package utils

import (
	"context"
	"github.com/coreos/etcd/clientv3"
	"github.com/sirupsen/logrus"
	"time"
)

var HasWatch = false

type EtcdUtil struct {

}

func (e *EtcdUtil) StartWatch(key string)  (clientv3.<PERSON><PERSON><PERSON>, error) {
	client, err := clientv3.New(clientv3.Config{
		//Endpoints:   []string{"***********:23790", "***********:23790", "***********:23790"},
		Endpoints:   []string{"***********:23790", "***********:23790", "***********:23790"},
		// Endpoints: []string{"localhost:2379", "localhost:22379", "localhost:32379"}
		DialTimeout: 5 * time.Second,
		//Username: "root",
		//Password: "zv0Kx2PHrg",
	})

	if err != nil {
		logrus.Errorf("init etcd client failed. reason: %v", err)
		return nil, err
	}

	watcher := clientv3.NewWatcher(client)
	return watcher.Watch(context.TODO(), key), nil
}
