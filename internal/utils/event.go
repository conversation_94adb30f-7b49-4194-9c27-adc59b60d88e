package utils

import (
	"bytes"
	"encoding/json"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/api"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/internal/config"
	"github.com/sirupsen/logrus"
	"io/ioutil"
	"net/http"
	"time"
)

var (
	KimEventUtils = NewKimEvent(config.KIM_WEBHOOK)
)

type KimEvent struct {
	Webhook string
}

func NewKimEvent(webhook string) *KimEvent {
	return &KimEvent{Webhook: webhook}
}

func (k *KimEvent) SendMsg(content string) error {
	kimMsgRequest := &api.KimMsgRequest{
		MsgType: config.KIM_MSG_TYPE,
		MD:      &api.Markdown{Content: content},
	}
	kimMsg, err := json.Marshal(kimMsgRequest)
	if err != nil { return err }
	payload := bytes.NewReader(kimMsg)
	request, err := http.NewRequest("POST", k.Webhook, payload)
	if err != nil { return err }
	request.Header.Add("Content-Type", "application/json")
	client := http.Client{
		Timeout:       time.Second *  10,
	}
	logrus.Info("send msg to kim webhook")
	response, err := client.Do(request)
	if err != nil {
		logrus.Errorf("send msg to kim webhook failed, reason: %s\n", err)
		return err
	}
	defer response.Body.Close()
	body, _ := ioutil.ReadAll(response.Body)
	logrus.Info(string(body))
	return nil
}


