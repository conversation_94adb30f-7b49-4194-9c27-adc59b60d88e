package utils

import (
	"os/exec"
	"strings"
)

type KvmUtil struct {
}

func NewKvmUtil() *KvmUtil {
	return &KvmUtil{}
}

func (k *KvmUtil) GetKvmMacList() []string {
	var vmIds []string
	var macs []string
	command := `virsh list`
	cmd := exec.Command("/bin/bash", "-c", command)
	output, _ := cmd.Output()
	vmList := strings.Split(string(output), "\n")
	for _, vm := range vmList {
		if strings.Contains(vm, "running") {
			vmIds = append(vmIds, strings.Split(vm, " ")[1])
		}
	}
	for _, vmId := range vmIds {
		command = "virsh domiflist " + vmId
		cmd = exec.Command("/bin/bash", "-c", command)
		output, _ := cmd.Output()
		netList := strings.Split(string(output), "\n")
		for _, net := range netList {
			if strings.Contains(net, "vnet") {
				netInfo := strings.Split(net, " ")
				macs = append(macs, netInfo[len(netInfo) - 1])
			}
		}
	}
	return macs
}