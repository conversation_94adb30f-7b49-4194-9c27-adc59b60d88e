package utils

import (
	"github.com/sirupsen/logrus"
	"os"
	"time"
)

type LogManager struct {
	LogPath string
	LogLimitInMB int64
	Period int64
}

func NewLogManager(logPath string , logLimitInMB int64, period int64) *LogManager {
	return &LogManager{
		LogPath:      logPath,
		LogLimitInMB: logLimitInMB,
		Period:       period,
	}
}

func (l *LogManager) LogCleanJob()  {
	for {
		stat, err := os.Stat(l.LogPath)
		if err != nil {
			logrus.Warnf("%s not existed!!!", l.<PERSON>g<PERSON>)
		}
		if stat != nil && stat.Size() >= l.LogLimitInMB * 1024 * 1024 {
			if err = os.Remove(l.LogPath); err != nil { logrus.Warnf("delete %s failed!!", l.<PERSON>g<PERSON>) }
		}
		if _, err := os.Create(l.<PERSON><PERSON>); err != nil {
			logrus.Warnf("create %s failed!!!", l.<PERSON>g<PERSON>)
		}
		time.Sleep(time.Duration(l.Period) * time.Second)
	}
}