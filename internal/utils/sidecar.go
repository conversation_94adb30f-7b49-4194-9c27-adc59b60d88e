package utils

import (
	"github.com/coreos/etcd/clientv3"
	"github.com/shirou/gopsutil/host"
	"github.com/sirupsen/logrus"
	"io"
	"git.corp.kuaishou.com/ks-ep/ops/cmdb-agent/internal/config"
	"net/http"
	"os"
	"time"
)

var SideCar = NewSidecar()

type Sidecar struct {
	EtcdUtil
}

func NewSidecar() *Sidecar {
	return &Sidecar{EtcdUtil{}}
}

func (s *Sidecar) Start()  {
	var (
		restartChan clientv3.Watch<PERSON>han
		updateChan clientv3.WatchChan
		startChan clientv3.Watch<PERSON>han
		stopChan clientv3.<PERSON><PERSON>han
		err error
	)
	if restartChan, err = s.StartWatch(config.AGENT_RESTART_KEY); err != nil {
		return
	}
	if updateChan, err = s.StartWatch(config.AGENT_UPDATE_KEY); err != nil {
		return
	}
	if startChan, err = s.StartWatch(config.AGENT_START_KEY); err != nil {
		return
	}
	if stop<PERSON>han, err = s.StartWatch(config.AGENT_STOP_KEY); err != nil {
		return
	}

	HasWatch = true
	logrus.Info("cmdb-agent sidecar started...")
	for  {
		select {
		case resp := <- restartChan:
			logrus.Info(resp)
			logrus.Warn("cmdb-agent receive restart signal...")
			//os.Exit(0)
		case resp := <- updateChan:
			logrus.Info(resp)
			logrus.Warn("cmdb-agent receive update signal...")
			//s.onUpdate()
		case resp := <- startChan:
			logrus.Info(resp)
			logrus.Warn("cmdb-agent receive start signal...")
		case resp := <- stopChan:
			logrus.Info(resp)
			logrus.Warn("cmdb-agent receive stop signal...")
			//os.Exit(0)
		default:
			time.Sleep(time.Second)
		}
		time.Sleep(time.Second)
	}
}

func (s *Sidecar) onUpdate() {
	resp, err := http.Get("https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/ops/cmdb-agent-linux-amd64")
	if err != nil {
		logrus.Error("Download file cmdb-agent-linux-amd64 failed")
		logrus.Warn(err)
		return
	}
	var filePath string
	info, err := host.Info()
	if info == nil {
		filePath = "/usr/sbin/cmdb-agent"
	} else {
		if info.OS == "linux" {
			filePath = "/usr/sbin/cmdb-agent"
		} else if info.OS == "darwin" {
			filePath = "cmdb-agent"
		} else {
			filePath = "cmdb-agent"
		}
	}
	err = os.Remove(filePath)
	if err != nil {
		logrus.Errorf("remove file %s failed\n", filePath)
		logrus.Warn(err)
		//continue
	}
	f, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY, 0755)
	if err != nil {
		logrus.Errorf("Create file %s failed\n", filePath)
		logrus.Warn(err)
		return
	}
	_, _ = io.Copy(f, resp.Body)
	os.Exit(0)
}
