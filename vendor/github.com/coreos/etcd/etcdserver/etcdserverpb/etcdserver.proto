syntax = "proto2";
package etcdserverpb;

import "gogoproto/gogo.proto";

option (gogoproto.marshaler_all) = true;
option (gogoproto.sizer_all) = true;
option (gogoproto.unmarshaler_all) = true;
option (gogoproto.goproto_getters_all) = false;

message Request {
	optional uint64 ID         =  1 [(gogoproto.nullable) = false];
	optional string Method     =  2 [(gogoproto.nullable) = false];
	optional string Path       =  3 [(gogoproto.nullable) = false];
	optional string Val        =  4 [(gogoproto.nullable) = false];
	optional bool   Dir        =  5 [(gogoproto.nullable) = false];
	optional string PrevValue  =  6 [(gogoproto.nullable) = false];
	optional uint64 PrevIndex  =  7 [(gogoproto.nullable) = false];
	optional bool   PrevExist  =  8 [(gogoproto.nullable) = true];
	optional int64  Expiration =  9 [(gogoproto.nullable) = false];
	optional bool   Wait       = 10 [(gogoproto.nullable) = false];
	optional uint64 Since      = 11 [(gogoproto.nullable) = false];
	optional bool   Recursive  = 12 [(gogoproto.nullable) = false];
	optional bool   Sorted     = 13 [(gogoproto.nullable) = false];
	optional bool   Quorum     = 14 [(gogoproto.nullable) = false];
	optional int64  Time       = 15 [(gogoproto.nullable) = false];
	optional bool   Stream     = 16 [(gogoproto.nullable) = false];
	optional bool   Refresh    = 17 [(gogoproto.nullable) = true];
}

message Metadata {
	optional uint64 NodeID    = 1 [(gogoproto.nullable) = false];
	optional uint64 ClusterID = 2 [(gogoproto.nullable) = false];
}
